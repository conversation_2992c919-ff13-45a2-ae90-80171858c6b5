const { OpenAI } = require("openai");
const PortkeyLangSmithWrapper = require("../ai/portkeyLangsmithWrapper");
const fs = require("fs");
require("dotenv").config();

// Initialize Portkey wrapper for assistant use case
const portkeyWrapper = new PortkeyLangSmithWrapper('assistant');

// Function to create a thread run with the specified parameters
async function getAssistantResponse(assistantId, json_string, usePortkey = true) {
  try {
    if (usePortkey) {
      // Use Portkey + LangSmith wrapper
      console.log("Using Portkey + LangSmith wrapper for assistant response");

      const messages = [{ role: "user", content: json_string }];
      const run = await portkeyWrapper.createAssistantRun(assistantId, messages);

      const result = run.usage || {};
      result["gptDetails"] = run.model;
      result["prompt"] = JSON.stringify(run.instructions || "");

      if (run.status === "completed" && run.message) {
        result["message"] = run.message.replace(/"""/g, " ");
        return result;
      } else {
        console.log("Thread run failed:", run.last_error?.message || "Unknown error");
        return result;
      }
    } else {
      // Fallback to original OpenAI implementation
      console.log("Using fallback OpenAI implementation");

      const apiKey = process.env.OPENAI_API_KEY;
      const open_ai = new OpenAI({
        apiKey: apiKey,
        default_headers: { "OpenAI-Beta": "assistants=v2" },
      });

      let run = await open_ai.beta.threads.createAndRun({
        assistant_id: assistantId,
        thread: {
          messages: [{ role: "user", content: json_string }],
        },
      });

      while (["queued", "in_progress", "cancelling"].includes(run.status)) {
        console.log("Waiting for thread run to complete...", run.status);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        run = await open_ai.beta.threads.runs.retrieve(run.thread_id, run.id);
      }

      console.log("Thread run completed:", run.status);
      const result = run.usage || {};
      result["gptDetails"] = run.model;
      result["prompt"] = JSON.stringify(run.instructions);

      if (run.status === "completed") {
        const messages = await open_ai.beta.threads.messages.list(run.thread_id);
        for (const message of messages.data.reverse()) {
          if (message.role === "assistant") {
            result["message"] = message.content[0].text.value.replace(/"""/g, " ");
            return result;
          }
        }
      } else {
        console.log("Thread run failed:", run.last_error?.message);
      }
      return result;
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);

    // If Portkey fails, try fallback
    if (usePortkey) {
      console.log("Portkey failed, trying fallback...");
      return getAssistantResponse(assistantId, json_string, false);
    }

    // Log specific error types
    if (error.response?.data?.error?.code === 'insufficient_funds') {
      console.log("Insufficient funds");
    }

    throw new Error("Error creating thread run: " + error.message);
  }
}

// Example usage
async function exampleUsage() {
  try {
    const json = fs.readFileSync("scrapeGPT/data.json", "utf8");
    const result = await run(JSON.stringify(json));
    console.log(result);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error in exampleUsage:", error);
  }
}

module.exports = {
  getAssistantResponse,
};
