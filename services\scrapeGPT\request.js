const { AzureOpenAI } = require("openai");
const PortkeyLangSmithWrapper = require("../ai/portkeyLangsmithWrapper");
const fs = require("fs");
require("dotenv").config();

// Initialize Portkey wrappers for different use cases
const portkeyWrapperChat = new PortkeyLangSmithWrapper('chat');
const portkeyWrapperAzure = new PortkeyLangSmithWrapper('azure');

async function getChatGPTResponse(system_prompt, user_prompt, usePortkey = true) {
  try {
    if (usePortkey) {
      // Use Portkey + LangSmith wrapper
      console.log("Using Portkey + LangSmith wrapper for chat completion");

      const messages = [
        {
          role: "system",
          content: system_prompt,
        },
        {
          role: "user",
          content: user_prompt,
        },
      ];

      // Determine which wrapper to use based on environment variables
      const useAzure = process.env.AZURE_OPENAI_ENDPOINT && process.env.AZURE_OPENAI_DEPLOYMENT;
      const wrapper = useAzure ? portkeyWrapperAzure : portkeyWrapperChat;

      const completion = await wrapper.createChatCompletion(messages, {
        temperature: 1,
        presence_penalty: 0,
        top_p: 1,
        max_tokens: 256,
      });

      const result = completion.usage || {};
      result["message"] = completion.choices[0]?.message?.content;
      result["prompt"] = messages;
      return result;

    } else {
      // Fallback to original Azure OpenAI implementation
      console.log("Using fallback Azure OpenAI implementation");

      const apiKey = process.env.OPENAI_API_KEY;
      const modelId = process.env.OPENAI_MODEL_ID;
      const azure_endpoint = process.env.AZURE_OPENAI_ENDPOINT;
      const azure_deployment = process.env.AZURE_OPENAI_DEPLOYMENT;
      const azure_api_version = process.env.AZURE_OPENAI_API_VERSION;

      const open_ai = new AzureOpenAI({
        apiKey: apiKey,
        endpoint: azure_endpoint,
        apiVersion: azure_api_version,
        deployment: azure_deployment,
      });

      const messages = [
        {
          role: "system",
          content: system_prompt,
        },
        {
          role: "user",
          content: user_prompt,
        },
      ];

      const completion = await open_ai.chat.completions.create({
        messages: messages,
        model: modelId,
        temperature: 1,
        presence_penalty: 0,
        top_p: 1,
        max_tokens: 256,
      });

      const result = completion.usage || {};
      result["message"] = completion.choices[0]?.message?.content;
      result["prompt"] = messages;
      return result;
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);

    // If Portkey fails, try fallback
    if (usePortkey) {
      console.log("Portkey failed, trying fallback...");
      return getChatGPTResponse(system_prompt, user_prompt, false);
    }

    throw new Error("Error fetching response from ChatGPT: " + error.message);
  }
}

async function main() {
  try {
    const systemPrompt = "You are a helpful assistant.";
    const userPrompt = "What is the capital of France?";

    const response = await getChatGPTResponse(systemPrompt, userPrompt);
    console.log("ChatGPT Response:", response.message);
    console.log("Token Usage:", {
      promptTokens: response.prompt_tokens,
      completionTokens: response.completion_tokens,
      totalTokens: response.total_tokens,
    });
  } catch (error) {
    console.error("Error:", error.message);
  }
}
module.exports = {
  getChatGPTResponse,
};
