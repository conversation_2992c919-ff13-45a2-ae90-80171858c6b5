const { AzureOpenAI } = require("openai");
const fs = require("fs");
require("dotenv").config();

async function getChatGPTResponse(system_prompt, user_prompt) {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    const modelId = process.env.OPENAI_MODEL_ID;
    const azure_endpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azure_deployment = process.env.AZURE_OPENAI_DEPLOYMENT;
    const azure_api_version = process.env.AZURE_OPENAI_API_VERSION;
    const open_ai = new AzureOpenAI({
      apiKey: apiKey,
      endpoint: azure_endpoint,
      apiVersion: azure_api_version,
      deployment: azure_deployment,
    });
    const messages = [
      {
        role: "system",
        content: system_prompt,
      },
      {
        role: "user",
        content: user_prompt,
      },
    ];
    const completion = await open_ai.chat.completions.create({
      messages: messages,
      model: modelId,
      temperature: 1,
      presence_penalty: 0,
      top_p: 1,
      max_tokens: 256,
    });

    result = completion.usage;
    result["message"] = completion.choices[0].message.content;
    result["prompt"] = messages;
    return result;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    throw new Error("Error fetching response from ChatGPT: " + error);
  }
}

async function main() {
  try {
    const systemPrompt = "You are a helpful assistant.";
    const userPrompt = "What is the capital of France?";

    const response = await getChatGPTResponse(systemPrompt, userPrompt);
    console.log("ChatGPT Response:", response.message);
    console.log("Token Usage:", {
      promptTokens: response.prompt_tokens,
      completionTokens: response.completion_tokens,
      totalTokens: response.total_tokens,
    });
  } catch (error) {
    console.error("Error:", error.message);
  }
}
module.exports = {
  getChatGPTResponse,
};
