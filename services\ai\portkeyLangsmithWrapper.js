const Portkey = require('portkey-ai').default;
const { Client } = require('langsmith');
const { getPortkeyConfig, getLangSmithConfig, validateConfig } = require('../../config/portkey');
require('dotenv').config();

/**
 * Portkey + LangSmith Wrapper for OpenAI
 * 
 * This wrapper combines Portkey's AI gateway functionality with LangSmith's
 * tracing and monitoring capabilities for comprehensive AI observability.
 */

class PortkeyLangSmithWrapper {
  constructor(useCase = 'chat') {
    this.useCase = useCase;
    this.portkeyConfig = getPortkeyConfig(useCase);
    this.langsmithConfig = getLangSmithConfig();
    
    // Validate configuration
    const validation = validateConfig();
    if (!validation.isValid) {
      console.warn('Portkey configuration validation failed:', validation.message);
    }
    
    // Initialize Portkey client
    this.portkey = new Portkey({
      apiKey: this.portkeyConfig.apiKey,
      baseURL: this.portkeyConfig.baseURL,
      virtualKey: this.portkeyConfig.virtualKey,
      config: this.buildPortkeyConfig(),
    });
    
    // Initialize LangSmith client
    this.langsmith = new Client({
      apiKey: this.langsmithConfig.apiKey,
      apiUrl: this.langsmithConfig.endpoint,
    });
    
    this.isTracingEnabled = this.langsmithConfig.tracingEnabled;
    this.projectName = this.langsmithConfig.projectName;
  }
  
  /**
   * Build Portkey configuration object
   */
  buildPortkeyConfig() {
    return {
      strategy: {
        mode: 'fallback',
      },
      targets: [
        {
          provider: this.portkeyConfig.provider,
          api_key: this.portkeyConfig.virtualKey,
          retry: this.portkeyConfig.retry,
          cache: this.portkeyConfig.cache,
          request_timeout: this.portkeyConfig.requestTimeout,
        },
      ],
      metadata: this.portkeyConfig.metadata,
    };
  }
  
  /**
   * Create a chat completion with Portkey and LangSmith tracing
   */
  async createChatCompletion(messages, options = {}) {
    const runId = this.generateRunId();
    const startTime = Date.now();
    
    try {
      // Start LangSmith trace
      let run = null;
      if (this.isTracingEnabled) {
        run = await this.startLangSmithRun(runId, 'chat_completion', {
          messages,
          model: options.model || this.portkeyConfig.model,
          temperature: options.temperature || this.portkeyConfig.temperature,
          max_tokens: options.maxTokens || this.portkeyConfig.maxTokens,
        });
      }
      
      // Prepare request parameters
      const requestParams = {
        model: options.model || this.portkeyConfig.model,
        messages,
        temperature: options.temperature || this.portkeyConfig.temperature,
        max_tokens: options.maxTokens || this.portkeyConfig.maxTokens,
        ...options,
      };
      
      // Add Portkey-specific headers
      const headers = {
        'x-portkey-trace-id': runId,
        'x-portkey-metadata': JSON.stringify({
          useCase: this.useCase,
          langsmithRunId: runId,
          ...this.portkeyConfig.metadata,
        }),
      };
      
      // Make request through Portkey
      const response = await this.portkey.chat.completions.create(requestParams, {
        headers,
      });
      
      // Calculate metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // End LangSmith trace with success
      if (this.isTracingEnabled && run) {
        await this.endLangSmithRun(runId, {
          outputs: {
            response: response.choices[0]?.message?.content,
            usage: response.usage,
            model: response.model,
          },
          duration,
          status: 'success',
        });
      }
      
      // Log metrics
      this.logMetrics('chat_completion', {
        duration,
        tokens: response.usage,
        model: response.model,
        success: true,
      });
      
      return response;
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // End LangSmith trace with error
      if (this.isTracingEnabled) {
        await this.endLangSmithRun(runId, {
          duration,
          status: 'error',
          error: error.message,
        });
      }
      
      // Log error metrics
      this.logMetrics('chat_completion', {
        duration,
        success: false,
        error: error.message,
      });
      
      throw error;
    }
  }
  
  /**
   * Create an assistant thread run with Portkey and LangSmith tracing
   */
  async createAssistantRun(assistantId, messages, options = {}) {
    const runId = this.generateRunId();
    const startTime = Date.now();
    
    try {
      // Start LangSmith trace
      let run = null;
      if (this.isTracingEnabled) {
        run = await this.startLangSmithRun(runId, 'assistant_run', {
          assistantId,
          messages,
          ...options,
        });
      }
      
      // Add Portkey-specific headers
      const headers = {
        'x-portkey-trace-id': runId,
        'x-portkey-metadata': JSON.stringify({
          useCase: this.useCase,
          langsmithRunId: runId,
          assistantId,
          ...this.portkeyConfig.metadata,
        }),
        'OpenAI-Beta': 'assistants=v2',
      };
      
      // Create thread and run through Portkey
      const threadRun = await this.portkey.beta.threads.createAndRun({
        assistant_id: assistantId,
        thread: { messages },
        ...options,
      }, { headers });
      
      // Wait for completion
      let completedRun = threadRun;
      while (['queued', 'in_progress', 'cancelling'].includes(completedRun.status)) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        completedRun = await this.portkey.beta.threads.runs.retrieve(
          completedRun.thread_id,
          completedRun.id,
          { headers }
        );
      }
      
      // Get messages if completed successfully
      let responseMessage = null;
      if (completedRun.status === 'completed') {
        const threadMessages = await this.portkey.beta.threads.messages.list(
          completedRun.thread_id,
          { headers }
        );
        
        for (const message of threadMessages.data.reverse()) {
          if (message.role === 'assistant') {
            responseMessage = message.content[0]?.text?.value;
            break;
          }
        }
      }
      
      // Calculate metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // End LangSmith trace
      if (this.isTracingEnabled && run) {
        await this.endLangSmithRun(runId, {
          outputs: {
            response: responseMessage,
            usage: completedRun.usage,
            status: completedRun.status,
            model: completedRun.model,
          },
          duration,
          status: completedRun.status === 'completed' ? 'success' : 'error',
        });
      }
      
      // Log metrics
      this.logMetrics('assistant_run', {
        duration,
        tokens: completedRun.usage,
        model: completedRun.model,
        success: completedRun.status === 'completed',
      });
      
      return {
        ...completedRun,
        message: responseMessage,
      };
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // End LangSmith trace with error
      if (this.isTracingEnabled) {
        await this.endLangSmithRun(runId, {
          duration,
          status: 'error',
          error: error.message,
        });
      }
      
      // Log error metrics
      this.logMetrics('assistant_run', {
        duration,
        success: false,
        error: error.message,
      });
      
      throw error;
    }
  }
  
  /**
   * Start a LangSmith run
   */
  async startLangSmithRun(runId, runType, inputs) {
    if (!this.isTracingEnabled) return null;
    
    try {
      return await this.langsmith.createRun({
        id: runId,
        project_name: this.projectName,
        name: `${this.useCase}_${runType}`,
        run_type: 'llm',
        inputs,
        start_time: new Date(),
        extra: {
          portkey: true,
          useCase: this.useCase,
          provider: this.portkeyConfig.provider,
        },
      });
    } catch (error) {
      console.warn('Failed to start LangSmith run:', error.message);
      return null;
    }
  }
  
  /**
   * End a LangSmith run
   */
  async endLangSmithRun(runId, data) {
    if (!this.isTracingEnabled) return;
    
    try {
      await this.langsmith.updateRun(runId, {
        outputs: data.outputs,
        end_time: new Date(),
        error: data.error,
        extra: {
          duration_ms: data.duration,
          status: data.status,
        },
      });
    } catch (error) {
      console.warn('Failed to end LangSmith run:', error.message);
    }
  }
  
  /**
   * Generate a unique run ID
   */
  generateRunId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Log metrics
   */
  logMetrics(operation, metrics) {
    if (this.portkeyConfig.logging?.enabled) {
      console.log(`[PortkeyLangSmith] ${operation}:`, {
        useCase: this.useCase,
        timestamp: new Date().toISOString(),
        ...metrics,
      });
    }
  }
  
  /**
   * Get usage statistics
   */
  async getUsageStats(timeframe = '24h') {
    try {
      // This would typically call Portkey's analytics API
      // For now, we'll return a placeholder
      return {
        timeframe,
        requests: 0,
        tokens: 0,
        cost: 0,
        errors: 0,
        avgLatency: 0,
      };
    } catch (error) {
      console.warn('Failed to get usage stats:', error.message);
      return null;
    }
  }
}

module.exports = PortkeyLangSmithWrapper;
